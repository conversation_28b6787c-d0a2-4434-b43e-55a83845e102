{"name": "erp-backend", "version": "1.0.0", "description": "ERP System Backend API", "main": "src/app.js", "scripts": {"start": "node src/app-simple.js", "dev": "nodemon src/app-simple.js", "dev-full": "nodemon src/app.js", "setup": "node scripts/setup-database.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["erp", "backend", "api", "express", "postgresql"], "author": "", "license": "MIT", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "pg": "^8.16.0", "sequelize": "^6.37.7"}, "devDependencies": {"concurrently": "^9.1.2", "nodemon": "^3.1.10"}}