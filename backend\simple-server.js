const http = require('http');
const url = require('url');

const PORT = 5000;

// Simple CORS headers
const setCORSHeaders = (res) => {
  res.setHeader('Access-Control-Allow-Origin', 'http://localhost:3000');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Access-Control-Allow-Credentials', 'true');
};

// Parse JSON body
const parseBody = (req) => {
  return new Promise((resolve) => {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        resolve(JSON.parse(body));
      } catch (e) {
        resolve({});
      }
    });
  });
};

// Send JSON response
const sendJSON = (res, statusCode, data) => {
  res.statusCode = statusCode;
  res.setHeader('Content-Type', 'application/json');
  res.end(JSON.stringify(data));
};

// Create server
const server = http.createServer(async (req, res) => {
  setCORSHeaders(res);
  
  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.statusCode = 200;
    res.end();
    return;
  }
  
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;
  
  console.log(`${method} ${path}`);
  
  try {
    // Health check
    if (path === '/health' && method === 'GET') {
      sendJSON(res, 200, {
        status: 'OK',
        message: 'ERP System API is running',
        timestamp: new Date().toISOString()
      });
      return;
    }
    
    // API info
    if (path === '/api' && method === 'GET') {
      sendJSON(res, 200, {
        message: 'ERP System API',
        version: '1.0.0',
        status: 'running'
      });
      return;
    }
    
    // Login endpoint
    if (path === '/api/auth/login' && method === 'POST') {
      const body = await parseBody(req);
      const { email, password } = body;
      
      if (email === '<EMAIL>' && password === 'admin123') {
        sendJSON(res, 200, {
          message: 'Login successful',
          token: 'mock-jwt-token-' + Date.now(),
          user: {
            id: '1',
            username: 'admin',
            email: '<EMAIL>',
            firstName: 'System',
            lastName: 'Administrator',
            role: 'admin'
          }
        });
      } else {
        sendJSON(res, 401, {
          error: 'Invalid credentials',
          message: 'Email or password is incorrect'
        });
      }
      return;
    }
    
    // Dashboard data
    if (path === '/api/reports/dashboard' && method === 'GET') {
      sendJSON(res, 200, {
        sales: {
          today: { count: 5, total: 12500 },
          month: { count: 45, total: 125000 }
        },
        inventory: { lowStockCount: 3 },
        support: { openTickets: 8 },
        recentActivities: {
          sales: [
            {
              id: '1',
              invoiceNumber: 'INV001',
              totalAmount: 2500,
              status: 'paid',
              customer: { name: 'شركة الرياض للتجارة' }
            },
            {
              id: '2',
              invoiceNumber: 'INV002',
              totalAmount: 1800,
              status: 'partially_paid',
              customer: { name: 'محمد أحمد السعيد' }
            }
          ],
          tickets: [
            {
              id: '1',
              title: 'مشكلة في الطابعة',
              priority: 'high',
              customer: { name: 'محمد أحمد' }
            },
            {
              id: '2',
              title: 'استفسار عن الفاتورة',
              priority: 'medium',
              customer: { name: 'شركة الدمام' }
            }
          ]
        }
      });
      return;
    }
    
    // 404 for all other routes
    sendJSON(res, 404, {
      error: 'Route not found',
      message: `Cannot ${method} ${path}`
    });
    
  } catch (error) {
    console.error('Server error:', error);
    sendJSON(res, 500, {
      error: 'Internal Server Error',
      message: error.message
    });
  }
});

// Start server
server.listen(PORT, () => {
  console.log(`🚀 ERP Backend Server is running on http://localhost:${PORT}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/health`);
  console.log(`📊 API: http://localhost:${PORT}/api`);
  console.log(`🔑 Login: POST http://localhost:${PORT}/api/auth/login`);
  console.log(`📈 Dashboard: GET http://localhost:${PORT}/api/reports/dashboard`);
});

// Handle server errors
server.on('error', (error) => {
  console.error('Server error:', error);
});

module.exports = server;
