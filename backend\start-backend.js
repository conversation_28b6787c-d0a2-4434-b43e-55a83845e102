const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = 5000;

// CORS configuration
app.use(cors({
  origin: 'http://localhost:3000',
  credentials: true
}));

// Body parsing middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    message: 'ERP System API is running',
    timestamp: new Date().toISOString()
  });
});

// Basic API endpoint
app.get('/api', (req, res) => {
  res.json({
    message: 'ERP System API',
    version: '1.0.0',
    status: 'running'
  });
});

// Mock login endpoint
app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;
  
  if (email === '<EMAIL>' && password === 'admin123') {
    res.json({
      message: 'Login successful',
      token: 'mock-jwt-token',
      user: {
        id: '1',
        username: 'admin',
        email: '<EMAIL>',
        firstName: 'System',
        lastName: 'Administrator',
        role: 'admin'
      }
    });
  } else {
    res.status(401).json({
      error: 'Invalid credentials',
      message: 'Email or password is incorrect'
    });
  }
});

// Mock dashboard data
app.get('/api/reports/dashboard', (req, res) => {
  res.json({
    sales: {
      today: { count: 5, total: 12500 },
      month: { count: 45, total: 125000 }
    },
    inventory: { lowStockCount: 3 },
    support: { openTickets: 8 },
    recentActivities: {
      sales: [
        {
          id: '1',
          invoiceNumber: 'INV001',
          totalAmount: 2500,
          status: 'paid',
          customer: { name: 'شركة الرياض للتجارة' }
        }
      ],
      tickets: [
        {
          id: '1',
          title: 'مشكلة في الطابعة',
          priority: 'high',
          customer: { name: 'محمد أحمد' }
        }
      ]
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    message: `Cannot ${req.method} ${req.originalUrl}`
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 ERP Backend Server is running on http://localhost:${PORT}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/health`);
  console.log(`📊 API: http://localhost:${PORT}/api`);
});

module.exports = app;
