[{"C:\\Users\\<USER>\\Desktop\\ERP SYSTEM\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\ERP SYSTEM\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\ERP SYSTEM\\frontend\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\ERP SYSTEM\\frontend\\src\\context\\AuthContext.js": "4", "C:\\Users\\<USER>\\Desktop\\ERP SYSTEM\\frontend\\src\\pages\\Login.js": "5", "C:\\Users\\<USER>\\Desktop\\ERP SYSTEM\\frontend\\src\\pages\\Dashboard.js": "6", "C:\\Users\\<USER>\\Desktop\\ERP SYSTEM\\frontend\\src\\components\\ProtectedRoute.js": "7", "C:\\Users\\<USER>\\Desktop\\ERP SYSTEM\\frontend\\src\\components\\Layout\\Layout.js": "8", "C:\\Users\\<USER>\\Desktop\\ERP SYSTEM\\frontend\\src\\components\\Layout\\Sidebar.js": "9", "C:\\Users\\<USER>\\Desktop\\ERP SYSTEM\\frontend\\src\\components\\Layout\\Header.js": "10", "C:\\Users\\<USER>\\Desktop\\ERP SYSTEM\\frontend\\src\\services\\api.js": "11"}, {"size": 535, "mtime": 1749557653447, "results": "12", "hashOfConfig": "13"}, {"size": 1404, "mtime": 1749558118961, "results": "14", "hashOfConfig": "13"}, {"size": 362, "mtime": 1749557653652, "results": "15", "hashOfConfig": "13"}, {"size": 5993, "mtime": 1749557973317, "results": "16", "hashOfConfig": "13"}, {"size": 5755, "mtime": 1749558051678, "results": "17", "hashOfConfig": "13"}, {"size": 9512, "mtime": 1749558091265, "results": "18", "hashOfConfig": "13"}, {"size": 1167, "mtime": 1749558104632, "results": "19", "hashOfConfig": "13"}, {"size": 779, "mtime": 1749557984469, "results": "20", "hashOfConfig": "13"}, {"size": 5135, "mtime": 1749558027376, "results": "21", "hashOfConfig": "13"}, {"size": 3404, "mtime": 1749558002496, "results": "22", "hashOfConfig": "13"}, {"size": 4697, "mtime": 1749557948270, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1sog9lj", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\ERP SYSTEM\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\ERP SYSTEM\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\ERP SYSTEM\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\ERP SYSTEM\\frontend\\src\\context\\AuthContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\ERP SYSTEM\\frontend\\src\\pages\\Login.js", [], [], "C:\\Users\\<USER>\\Desktop\\ERP SYSTEM\\frontend\\src\\pages\\Dashboard.js", [], [], "C:\\Users\\<USER>\\Desktop\\ERP SYSTEM\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Desktop\\ERP SYSTEM\\frontend\\src\\components\\Layout\\Layout.js", [], [], "C:\\Users\\<USER>\\Desktop\\ERP SYSTEM\\frontend\\src\\components\\Layout\\Sidebar.js", [], [], "C:\\Users\\<USER>\\Desktop\\ERP SYSTEM\\frontend\\src\\components\\Layout\\Header.js", [], [], "C:\\Users\\<USER>\\Desktop\\ERP SYSTEM\\frontend\\src\\services\\api.js", [], []]