<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام ERP - اختبار</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen flex items-center justify-center">
        <div class="max-w-md w-full space-y-8">
            <div class="text-center">
                <div class="mx-auto h-12 w-12 bg-blue-600 rounded-lg flex items-center justify-center">
                    <span class="text-white font-bold text-xl">E</span>
                </div>
                <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                    نظام ERP
                </h2>
                <p class="mt-2 text-center text-sm text-gray-600">
                    اختبار النظام
                </p>
            </div>
            
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">حالة النظام</h3>
                
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Frontend</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            يعمل
                        </span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Backend</span>
                        <span id="backend-status" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            جاري التحقق...
                        </span>
                    </div>
                </div>
                
                <div class="mt-6">
                    <button onclick="testBackend()" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        اختبار الاتصال بـ Backend
                    </button>
                </div>
                
                <div class="mt-4">
                    <button onclick="testLogin()" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                        اختبار تسجيل الدخول
                    </button>
                </div>
                
                <div id="result" class="mt-4 hidden">
                    <div class="bg-gray-50 rounded-md p-3">
                        <pre id="result-text" class="text-sm text-gray-800 whitespace-pre-wrap"></pre>
                    </div>
                </div>
            </div>
            
            <div class="text-center">
                <p class="text-xs text-gray-500">
                    Backend: <a href="http://localhost:5000/health" target="_blank" class="text-blue-600 hover:text-blue-500">http://localhost:5000</a>
                </p>
            </div>
        </div>
    </div>

    <script>
        async function testBackend() {
            const statusEl = document.getElementById('backend-status');
            const resultEl = document.getElementById('result');
            const resultText = document.getElementById('result-text');
            
            statusEl.textContent = 'جاري التحقق...';
            statusEl.className = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800';
            
            try {
                const response = await fetch('http://localhost:5000/health');
                const data = await response.json();
                
                statusEl.textContent = 'يعمل';
                statusEl.className = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800';
                
                resultEl.classList.remove('hidden');
                resultText.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                statusEl.textContent = 'لا يعمل';
                statusEl.className = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800';
                
                resultEl.classList.remove('hidden');
                resultText.textContent = 'خطأ: ' + error.message;
            }
        }
        
        async function testLogin() {
            const resultEl = document.getElementById('result');
            const resultText = document.getElementById('result-text');
            
            try {
                const response = await fetch('http://localhost:5000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'admin123'
                    })
                });
                
                const data = await response.json();
                
                resultEl.classList.remove('hidden');
                resultText.textContent = 'نتيجة تسجيل الدخول:\n' + JSON.stringify(data, null, 2);
            } catch (error) {
                resultEl.classList.remove('hidden');
                resultText.textContent = 'خطأ في تسجيل الدخول: ' + error.message;
            }
        }
        
        // Test backend on page load
        window.onload = function() {
            testBackend();
        };
    </script>
</body>
</html>
